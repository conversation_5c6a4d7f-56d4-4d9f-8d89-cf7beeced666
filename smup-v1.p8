pico-8 cartridge // http://www.pico-8.com
version 42
__lua__
-- arcade shooter

function _init()

  -- starting position of player
  plr={sprite=8, x=63, y=110}

  -- initialise enemies
  init_enemies()
    
  -- game state
  game="playing"
  score = 0
   
end


function _update()

    move_player()

    update_enemies()

end

function _draw()
    cls()

    if game == "playing" then
        spr(plr.sprite,plr.x,plr.y)
        draw_enemies()
        display_score()
        shoot() -- passing in the y position of the player

    elseif game == "game over" then
        print("you loose",45,60,11)
         display_score()

    elseif game == "win" then
        print("you win",45,60,11)
    end

    if #enemies == 0 then
        game="win"
        display_score()
    end


end









-->8
-- enemies --

function init_enemies()
    enemies={}
    total_enemies=10
    speed_y=0.2
    speed_x=rnd(1)
    
    for e=1,total_enemies do
        add(
        enemies,{x=flr(rnd(120)), y=flr(rnd(40)), life=5, sx=speed_x, sy=speed_y})
        -- returns a random number between 0 and 1 (fraction)
    end

end

function update_enemies()

    -- do for all enemies
    for enemy in all(enemies) do
        -- move the enemy
        enemy.x+=enemy.sx
        enemy.y+=enemy.sy

    -- bounce off the sides
        if enemy.x<0 or enemy.x>120 then
            enemy.sx=-enemy.sx
        end
        
        -- if it goes below the player, game over
        if enemy.y > 110 then
            game = "game over" 
        end
    end

    -- laser collision

    for enemy in all(enemies) do
        if btnp(5) then
            if abs(plr.x-enemy.x)<8 then
                enemy.life-=1
                if enemy.life<1 then
                    del(enemies,enemy)
                    score+=10
                end
            end
        end
    end

end


function draw_enemies()

    for e in all(enemies) do
            if e.life == 4 then
                spr(25,e.x,e.y)
            elseif e.life == 3 then
                spr(26,e.x,e.y)
            elseif e.life == 2 then
                spr(27,e.x,e.y)
            elseif e.life == 1 then
                spr(28,e.x,e.y)
            else
              spr(24,e.x,e.y)
            end
    end

end

-->8
-- move the player

function move_player()
    if btn(0) then
        if plr.x>0 then
        plr.x-=1
        end
    elseif btn(1) then
       if plr.x<120 then
        plr.x+=1
       end
    end
end
-->8
-- shoot laser --

function shoot()

    laser_color=11
	
    if btnp(5) then
    
    -- shoot laser
    line(plr.x,plr.y+1,plr.x,0,laser_color)
    line(plr.x+7,plr.y+1,plr.x+7,0,laser_color)

    end
	
end
-->8
-- score --

function display_score()
    print("score: "..score,0,0,10)
end

__gfx__
00000000000000000000000000000000000000000000000000000000000000000001100000000000000000000000000000000000000000000000000000000000
00000000000000000000000000000000000000000000000000000000000000000018810000000000000000000000000000000000000000000000000000000000
00700700000000000000000000000000000000000000000000000000000000001088880100000000000000000000000000000000000000000000000000000000
00077000000000000000000000000000a000000a0000000000000000000000008086680800000000000000000000000000000000000000000000000000000000
00077000000000000000000000000000a000000a000000000000000000000000888aa88800000000000000000000000000000000000000000000000000000000
00700700000000000000000000000000a000000a0000000000000000000000001888888100000000000000000000000000000000000000000000000000000000
00000000000000000000000000000000a000000a0000000000000000000000000188881000000000000000000000000000000000000000000000000000000000
00000000000000000000000000000000a000000a000000000000000000000000001dd10000000000000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000000000000000000000dddd0000dddd0000dddd0000dddd0000dddd00000000000000000000000000
00000000000000000000000000000000000000000000000000000000000000000dddddd00dddddd00dddddd00dddddd00dddddd0000000000000000000000000
0000000000000000000000000000000000000000000000000000000000000000dd0dd0dddd0dd0dddd0dd0dddd0dd0dddd0dd0dd000000000000000000000000
0000000000000000000000000000000000000000000000000000000000000000dddddddddddddddddddddddddddddddddd0dd0dd000000000000000000000000
0000000000000000000000000000000000000000000000000000000000000000dddddddddddddddddddddddddddddddddddddddd000000000000000000000000
0000000000000000000000000000000000000000000000000000000000000000d0d00d0dd0d0000d00d0000d00d0000000000000000000000000000000000000
0000000000000000000000000000000000000000000000000000000000000000d0d00d0dd0d0000d00d0000d00d0000000000000000000000000000000000000
000000000000000000000000000000000000000000000000000000000000000000d00d0000d0000000d0000000d0000000000000000000000000000000000000

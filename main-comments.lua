function _init()

    -- starting position of player
    plr={sprite=8, x=63, y=110}

    -- initialise enemies
    init_enemies()

    bullets={}

    -- starting position of bullets
    bposx=63
	bposy=110

    -- initialise bullets
	-- init_bullets()
    
    -- game state
    game="playing"
    score = 0
   

end


function _update()

    move_player()

    update_enemies()
    
	update_bullets()

end

function _draw()
    cls()

    if game == "playing" then
        spr(plr.sprite,plr.x,plr.y)
        draw_enemies()
        display_score()
        draw_bullets()
        shoot(bposx) -- passing in the y position of the player

    elseif game == "game over" then
        print("you loose",45,60,11)
         display_score()

    elseif game == "win" then
        print("you win",45,60,11)
    end

    if #enemies == 0 then
        game="win"
        display_score()
    end


end



--move the player left and right

function move_player()
    if btn(0) then
        if plr.x>0 then
        plr.x-=1
        end
    elseif btn(1) then
       if plr.x<120 then
        plr.x+=1
       end
    end
end


--enemies--

function init_enemies()
    enemies={}
    total_enemies=10
    speed_y=0.2
    speed_x=rnd(1)
    
    for e=1,total_enemies do
        add(
        enemies,{x=flr(rnd(120)), y=flr(rnd(40)), life=5, sx=speed_x, sy=speed_y})
        -- returns a random number between 0 and 1 (fraction)
    end
    -- stages before below
    -- add(enemies,{x=63, y=30})
    -- en={x=63, y=30}
end

function update_enemies()
    -- NEED TO break this down

    -- do for all enemies
    for enemy in all(enemies) do
        -- move the enemy
        enemy.x+=enemy.sx
        enemy.y+=enemy.sy

    -- bounce off the sides
        if enemy.x<0 or enemy.x>120 then
            enemy.sx=-enemy.sx
        end
        
        -- if it goes below the player, game over
        if enemy.y > 110 then
            game = "game over" 
        end
    end

    -- laser collision

    for enemy in all(enemies) do
        if btnp(5) then
            if abs(plr.x-enemy.x)<8 then
                enemy.life-=1
                if enemy.life<1 then
                    del(enemies,enemy)
                    score+=10
                end
            end
        end
    end

    -- update collision for bullets

    -- for e in all(enemies) do
    -- if btnp(5) then
    --   if abs(plr.x-e.x)<8 then
    --     printh("die")
    --   end
    -- end
    -- end

end

function draw_enemies()

    for e in all(enemies) do
            if e.life == 4 then
                spr(25,e.x,e.y)
            elseif e.life == 3 then
                spr(26,e.x,e.y)
            elseif e.life == 2 then
                spr(27,e.x,e.y)
            elseif e.life == 1 then
                spr(28,e.x,e.y)
            else
              spr(24,e.x,e.y)
            end
    end

    --  if e.life > 0 then
    --     spr(24,e.x,e.y)
    -- end

end


--bullets--

-- function init_bullets()
-- 	bullets={}
-- end

function update_bullets()

    -- bullets starting position
	bposy=(plr.y)
    bposx=(plr.x)
	
    -- take one bullet from the table and animate it each frame
	for bullet in all(bullets) do
		-- bullet.x=bullet.x+bullet.speed
      bullet.y=bullet.y-bullet.speed

        -- bullet test
		-- bullet.y=bullet.x+1

        -- remove bullet from table if it goes off screen
		if bullet.x > 128 then
			del(bullets,bullet)
		end
	end
	
	
end

function draw_bullets()
	
    -- how to draw each bullet
	for bullet in all(bullets) do
		spr(4,bullet.x,bullet.y)
        -- spr(4,0,0) -- just draw the bullet
	end
	
end

--shoot--

function shoot(gx)

    laser_color=11
	
    if btnp(5) then
    
    -- add a bullet in the form of a table to the table
	-- add(bullets,{
	-- 	x=gx,
	-- 	y=110,
	-- 	speed=4
	-- })

    -- shoot laser
    line(plr.x,plr.y+1,plr.x,0,laser_color)
    line(plr.x+7,plr.y+1,plr.x+7,0,laser_color)

    end
	
end

--score--

function display_score()
    print("score: "..score,0,0,10)
end
